---
**文档类型**: 开发实施指南
**文档版本**: v2.0
**创建日期**: 2025-07-29
**最后更新**: 2025-07-29
**文档所有者**: 技术负责人
**审批状态**: 已审批
**变更说明**: 企业级开发实施指南重构，提供详细的开发步骤和最佳实践
---

# 🔧 ToDoListArea开发实施指南

## 🔗 相关文档链接

- [文档体系主索引](./00_文档体系主索引.md) - 查看完整文档体系
- [技术选型与架构设计](./03_技术选型与架构设计.md) - 查看技术架构
- [详细设计规格书](./04_详细设计规格书.md) - 查看系统设计
- [API接口设计规范](./05_API接口设计规范.md) - 查看接口规范

## 📋 目录

- [开发环境搭建](#开发环境搭建)
- [后端开发实施](#后端开发实施)
- [前端开发实施](#前端开发实施)
- [数据库开发](#数据库开发)
- [集成测试](#集成测试)
- [部署指南](#部署指南)

---

## 🛠️ 开发环境搭建

### 📋 环境要求清单

#### 开发工具要求
| 工具类别 | 推荐工具 | 版本要求 | 必需性 |
|----------|----------|----------|--------|
| **IDE** | Visual Studio 2022 | 17.8+ | 必需 |
| **代码编辑器** | VS Code | 1.85+ | 推荐 |
| **数据库工具** | SQL Server Management Studio | 19.0+ | 必需 |
| **API测试** | Postman | 10.0+ | 推荐 |
| **版本控制** | Git | 2.40+ | 必需 |
| **容器工具** | Docker Desktop | 4.25+ | 推荐 |

#### 运行时环境
| 组件 | 版本 | 下载地址 | 配置说明 |
|------|------|----------|----------|
| **.NET SDK** | 8.0+ | [下载](https://dotnet.microsoft.com/download) | 安装最新LTS版本 |
| **Node.js** | 18.0+ | [下载](https://nodejs.org/) | 包含npm包管理器 |
| **SQL Server** | 2022 | [下载](https://www.microsoft.com/sql-server) | 开发者版本免费 |
| **Redis** | 7.0+ | [下载](https://redis.io/download) | 可选：用于缓存 |

### 🔧 环境配置步骤

#### 步骤1: 基础环境安装
```bash
# 1. 验证.NET SDK安装
dotnet --version
# 预期输出: 8.0.x

# 2. 验证Node.js安装
node --version
npm --version
# 预期输出: v18.x.x, 9.x.x

# 3. 验证Git安装
git --version
# 预期输出: git version 2.x.x
```

#### 步骤2: 项目代码获取
```bash
# 1. 克隆项目仓库
git clone https://github.com/company/todolistarea.git
cd todolistarea

# 2. 检查分支状态
git branch -a
git checkout develop

# 3. 初始化子模块（如有）
git submodule update --init --recursive
```

#### 步骤3: 后端环境配置
```bash
# 1. 进入后端项目目录
cd src/ToDoListArea.API

# 2. 恢复NuGet包
dotnet restore

# 3. 构建项目验证
dotnet build

# 4. 配置用户机密
dotnet user-secrets init
dotnet user-secrets set "ConnectionStrings:DefaultConnection" "Server=localhost;Database=ToDoListArea;Trusted_Connection=true;"
dotnet user-secrets set "JwtSettings:SecretKey" "your-super-secret-key-here"
```

#### 步骤4: 前端环境配置
```bash
# 1. 进入前端项目目录
cd src/ToDoListArea.Web

# 2. 安装依赖包
npm install

# 3. 验证构建
npm run build

# 4. 配置环境变量
cp .env.example .env.local
# 编辑.env.local文件，配置API地址等
```

#### 步骤5: 数据库环境配置
```sql
-- 1. 创建数据库
CREATE DATABASE ToDoListArea;

-- 2. 创建开发用户（可选）
CREATE LOGIN tododev WITH PASSWORD = 'DevPassword123!';
USE ToDoListArea;
CREATE USER tododev FOR LOGIN tododev;
ALTER ROLE db_owner ADD MEMBER tododev;
```

```bash
# 3. 运行数据库迁移
cd src/ToDoListArea.API
dotnet ef database update
```

### ✅ 环境验证清单

#### 后端验证
```bash
# 1. 启动后端服务
cd src/ToDoListArea.API
dotnet run

# 2. 验证API可访问
curl https://localhost:7001/api/health
# 预期返回: {"status": "Healthy"}

# 3. 验证Swagger文档
# 浏览器访问: https://localhost:7001/swagger
```

#### 前端验证
```bash
# 1. 启动前端开发服务器
cd src/ToDoListArea.Web
npm run dev

# 2. 验证页面可访问
# 浏览器访问: http://localhost:5173

# 3. 验证热重载功能
# 修改任意组件文件，观察页面自动刷新
```

#### 数据库验证
```sql
-- 1. 验证表结构
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE';

-- 2. 验证示例数据
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM task_categories;
```

---

## ⚙️ 后端开发实施

### 🏗️ 项目结构创建

#### 解决方案架构搭建
```bash
# 1. 创建解决方案
dotnet new sln -n ToDoListArea

# 2. 创建项目结构
mkdir src tests docs

# 3. 创建各层项目
cd src
dotnet new webapi -n ToDoListArea.API
dotnet new classlib -n ToDoListArea.Core
dotnet new classlib -n ToDoListArea.Infrastructure
dotnet new classlib -n ToDoListArea.Shared

# 4. 添加项目到解决方案
cd ..
dotnet sln add src/ToDoListArea.API/ToDoListArea.API.csproj
dotnet sln add src/ToDoListArea.Core/ToDoListArea.Core.csproj
dotnet sln add src/ToDoListArea.Infrastructure/ToDoListArea.Infrastructure.csproj
dotnet sln add src/ToDoListArea.Shared/ToDoListArea.Shared.csproj

# 5. 配置项目引用
cd src/ToDoListArea.API
dotnet add reference ../ToDoListArea.Core/ToDoListArea.Core.csproj
dotnet add reference ../ToDoListArea.Infrastructure/ToDoListArea.Infrastructure.csproj
dotnet add reference ../ToDoListArea.Shared/ToDoListArea.Shared.csproj

cd ../ToDoListArea.Infrastructure
dotnet add reference ../ToDoListArea.Core/ToDoListArea.Core.csproj
dotnet add reference ../ToDoListArea.Shared/ToDoListArea.Shared.csproj

cd ../ToDoListArea.Core
dotnet add reference ../ToDoListArea.Shared/ToDoListArea.Shared.csproj
```

#### NuGet包依赖管理
```xml
<!-- ToDoListArea.API.csproj -->
<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
<PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />

<!-- ToDoListArea.Infrastructure.csproj -->
<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0" />
<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.0" />
<PackageReference Include="Hangfire.SqlServer" Version="1.8.6" />

<!-- ToDoListArea.Core.csproj -->
<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
<PackageReference Include="FluentValidation" Version="11.8.0" />
<PackageReference Include="AutoMapper" Version="12.0.1" />
```

### 🔧 核心组件实现

#### 1. 实体模型设计
```csharp
// ToDoListArea.Core/Entities/User.cs
public class User : IdentityUser<Guid>
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? AvatarUrl { get; set; }
    public string Timezone { get; set; } = "UTC";
    public string Language { get; set; } = "zh-CN";
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // 导航属性
    public virtual ICollection<TaskItem> Tasks { get; set; } = new List<TaskItem>();
    public virtual UserProfile? Profile { get; set; }
}

// ToDoListArea.Core/Entities/TaskItem.cs
public class TaskItem
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid UserId { get; set; }
    public Guid? ParentTaskId { get; set; }
    public Guid? CategoryId { get; set; }
    
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public TaskStatus Status { get; set; } = TaskStatus.Pending;
    public TaskPriority Priority { get; set; } = TaskPriority.Medium;
    
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public int? EstimatedDuration { get; set; }
    public int? ActualDuration { get; set; }
    
    public decimal CompletionPercentage { get; set; } = 0;
    public bool IsRecurring { get; set; } = false;
    public string? RecurrencePattern { get; set; }
    public string? Tags { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? CompletedAt { get; set; }
    
    // 导航属性
    public virtual User User { get; set; } = null!;
    public virtual TaskItem? ParentTask { get; set; }
    public virtual ICollection<TaskItem> SubTasks { get; set; } = new List<TaskItem>();
    public virtual TaskCategory? Category { get; set; }
}
```

#### 2. 数据访问层实现
```csharp
// ToDoListArea.Infrastructure/Data/ToDoListDbContext.cs
public class ToDoListDbContext : IdentityDbContext<User, IdentityRole<Guid>, Guid>
{
    public ToDoListDbContext(DbContextOptions<ToDoListDbContext> options) : base(options) { }
    
    public DbSet<TaskItem> Tasks { get; set; }
    public DbSet<TaskCategory> TaskCategories { get; set; }
    public DbSet<UserProfile> UserProfiles { get; set; }
    public DbSet<Reminder> Reminders { get; set; }
    
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        
        // 应用所有实体配置
        builder.ApplyConfigurationsFromAssembly(typeof(ToDoListDbContext).Assembly);
        
        // 重命名Identity表
        builder.Entity<User>().ToTable("users");
        builder.Entity<IdentityRole<Guid>>().ToTable("roles");
        builder.Entity<IdentityUserRole<Guid>>().ToTable("user_roles");
        builder.Entity<IdentityUserClaim<Guid>>().ToTable("user_claims");
        builder.Entity<IdentityUserLogin<Guid>>().ToTable("user_logins");
        builder.Entity<IdentityUserToken<Guid>>().ToTable("user_tokens");
        builder.Entity<IdentityRoleClaim<Guid>>().ToTable("role_claims");
    }
    
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // 自动更新时间戳
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is ITimestampEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));
        
        foreach (var entry in entries)
        {
            var entity = (ITimestampEntity)entry.Entity;
            
            if (entry.State == EntityState.Added)
            {
                entity.CreatedAt = DateTime.UtcNow;
            }
            
            entity.UpdatedAt = DateTime.UtcNow;
        }
        
        return await base.SaveChangesAsync(cancellationToken);
    }
}

// ToDoListArea.Infrastructure/Data/Configurations/TaskConfiguration.cs
public class TaskConfiguration : IEntityTypeConfiguration<TaskItem>
{
    public void Configure(EntityTypeBuilder<TaskItem> builder)
    {
        builder.ToTable("tasks");
        
        builder.HasKey(t => t.Id);
        
        builder.Property(t => t.Title)
            .IsRequired()
            .HasMaxLength(255);
        
        builder.Property(t => t.Description)
            .HasColumnType("NVARCHAR(MAX)");
        
        builder.Property(t => t.Status)
            .HasConversion<string>()
            .HasMaxLength(20);
        
        builder.Property(t => t.Priority)
            .HasConversion<string>()
            .HasMaxLength(10);
        
        builder.Property(t => t.EstimatedDuration)
            .HasColumnType("INTEGER");
        
        builder.Property(t => t.CompletionPercentage)
            .HasColumnType("DECIMAL(5,2)");
        
        // 关系配置
        builder.HasOne(t => t.User)
            .WithMany(u => u.Tasks)
            .HasForeignKey(t => t.UserId)
            .OnDelete(DeleteBehavior.Cascade);
        
        builder.HasOne(t => t.ParentTask)
            .WithMany(t => t.SubTasks)
            .HasForeignKey(t => t.ParentTaskId)
            .OnDelete(DeleteBehavior.NoAction);
        
        builder.HasOne(t => t.Category)
            .WithMany()
            .HasForeignKey(t => t.CategoryId)
            .OnDelete(DeleteBehavior.SetNull);
        
        // 索引配置
        builder.HasIndex(t => new { t.UserId, t.Status, t.Priority })
            .HasDatabaseName("idx_tasks_user_status_priority");
        
        builder.HasIndex(t => new { t.StartTime, t.EndTime })
            .HasDatabaseName("idx_tasks_time_range");
    }
}
```

#### 3. 仓储模式实现
```csharp
// ToDoListArea.Core/Interfaces/IRepository.cs
public interface IRepository<T> where T : class
{
    Task<T?> GetByIdAsync(Guid id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
    Task<bool> AnyAsync(Expression<Func<T, bool>> predicate);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);
    Task<PagedResult<T>> GetPagedAsync(int page, int pageSize, Expression<Func<T, bool>>? predicate = null);
    
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task UpdateAsync(T entity);
    Task UpdateRangeAsync(IEnumerable<T> entities);
    Task DeleteAsync(T entity);
    Task DeleteRangeAsync(IEnumerable<T> entities);
}

// ToDoListArea.Infrastructure/Repositories/Repository.cs
public class Repository<T> : IRepository<T> where T : class
{
    protected readonly ToDoListDbContext _context;
    protected readonly DbSet<T> _dbSet;
    
    public Repository(ToDoListDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }
    
    public virtual async Task<T?> GetByIdAsync(Guid id)
    {
        return await _dbSet.FindAsync(id);
    }
    
    public virtual async Task<IEnumerable<T>> GetAllAsync()
    {
        return await _dbSet.ToListAsync();
    }
    
    public virtual async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate)
    {
        return await _dbSet.Where(predicate).ToListAsync();
    }
    
    public virtual async Task<PagedResult<T>> GetPagedAsync(int page, int pageSize, Expression<Func<T, bool>>? predicate = null)
    {
        var query = _dbSet.AsQueryable();
        
        if (predicate != null)
        {
            query = query.Where(predicate);
        }
        
        var totalCount = await query.CountAsync();
        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
        
        return new PagedResult<T>
        {
            Items = items,
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
        };
    }
    
    public virtual async Task<T> AddAsync(T entity)
    {
        await _dbSet.AddAsync(entity);
        return entity;
    }
    
    public virtual async Task UpdateAsync(T entity)
    {
        _dbSet.Update(entity);
        await Task.CompletedTask;
    }
    
    public virtual async Task DeleteAsync(T entity)
    {
        _dbSet.Remove(entity);
        await Task.CompletedTask;
    }
}
```

#### 4. 业务服务实现
```csharp
// ToDoListArea.Core/Services/TaskService.cs
public class TaskService : ITaskService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;
    private readonly ILogger<TaskService> _logger;
    
    public TaskService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<TaskService> logger)
    {
        _unitOfWork = unitOfWork;
        _mapper = mapper;
        _logger = logger;
    }
    
    public async Task<TaskDto> CreateTaskAsync(CreateTaskRequest request)
    {
        _logger.LogInformation("Creating task: {Title}", request.Title);
        
        // 验证请求
        var validator = new CreateTaskRequestValidator();
        var validationResult = await validator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            throw new ValidationException(validationResult.Errors);
        }
        
        // 检查时间冲突
        if (request.StartTime.HasValue && request.EndTime.HasValue)
        {
            var hasConflict = await CheckTimeConflictAsync(request.UserId, request.StartTime.Value, request.EndTime.Value);
            if (hasConflict)
            {
                throw new BusinessException("任务时间与现有任务冲突");
            }
        }
        
        // 创建任务实体
        var task = _mapper.Map<TaskItem>(request);
        task.Id = Guid.NewGuid();
        task.CreatedAt = DateTime.UtcNow;
        task.UpdatedAt = DateTime.UtcNow;
        
        // 保存到数据库
        await _unitOfWork.Tasks.AddAsync(task);
        await _unitOfWork.SaveChangesAsync();
        
        _logger.LogInformation("Task created successfully: {TaskId}", task.Id);
        
        return _mapper.Map<TaskDto>(task);
    }
    
    public async Task<PagedResult<TaskDto>> GetTasksAsync(TaskQueryRequest request)
    {
        _logger.LogInformation("Getting tasks for user: {UserId}", request.UserId);
        
        Expression<Func<TaskItem, bool>> predicate = t => t.UserId == request.UserId;
        
        // 应用筛选条件
        if (request.Status.HasValue)
        {
            predicate = predicate.And(t => t.Status == request.Status.Value);
        }
        
        if (request.Priority.HasValue)
        {
            predicate = predicate.And(t => t.Priority == request.Priority.Value);
        }
        
        if (request.CategoryId.HasValue)
        {
            predicate = predicate.And(t => t.CategoryId == request.CategoryId.Value);
        }
        
        if (request.StartDate.HasValue)
        {
            predicate = predicate.And(t => t.StartTime >= request.StartDate.Value);
        }
        
        if (request.EndDate.HasValue)
        {
            predicate = predicate.And(t => t.EndTime <= request.EndDate.Value);
        }
        
        if (!string.IsNullOrEmpty(request.Search))
        {
            predicate = predicate.And(t => t.Title.Contains(request.Search) || 
                                          (t.Description != null && t.Description.Contains(request.Search)));
        }
        
        var result = await _unitOfWork.Tasks.GetPagedAsync(request.Page, request.PageSize, predicate);
        
        return new PagedResult<TaskDto>
        {
            Items = _mapper.Map<List<TaskDto>>(result.Items),
            TotalCount = result.TotalCount,
            Page = result.Page,
            PageSize = result.PageSize,
            TotalPages = result.TotalPages
        };
    }
    
    private async Task<bool> CheckTimeConflictAsync(Guid userId, DateTime startTime, DateTime endTime)
    {
        return await _unitOfWork.Tasks.AnyAsync(t => 
            t.UserId == userId &&
            t.StartTime < endTime &&
            t.EndTime > startTime);
    }
}
```

#### 5. API控制器实现
```csharp
// ToDoListArea.API/Controllers/TasksController.cs
[ApiController]
[Route("api/v1/[controller]")]
[Authorize]
public class TasksController : ControllerBase
{
    private readonly ITaskService _taskService;
    private readonly ILogger<TasksController> _logger;
    
    public TasksController(ITaskService taskService, ILogger<TasksController> logger)
    {
        _taskService = taskService;
        _logger = logger;
    }
    
    /// <summary>
    /// 创建新任务
    /// </summary>
    /// <param name="request">任务创建请求</param>
    /// <returns>创建的任务信息</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<TaskDto>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ApiResponse<TaskDto>>> CreateTask([FromBody] CreateTaskRequest request)
    {
        try
        {
            request.UserId = GetCurrentUserId();
            var task = await _taskService.CreateTaskAsync(request);
            
            return CreatedAtAction(
                nameof(GetTask),
                new { id = task.Id },
                ApiResponse<TaskDto>.Success(task, "任务创建成功"));
        }
        catch (ValidationException ex)
        {
            return BadRequest(ApiResponse.Error("VALIDATION_ERROR", ex.Message));
        }
        catch (BusinessException ex)
        {
            return BadRequest(ApiResponse.Error("BUSINESS_ERROR", ex.Message));
        }
    }
    
    /// <summary>
    /// 获取任务列表
    /// </summary>
    /// <param name="request">查询参数</param>
    /// <returns>任务列表</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<TaskDto>>), StatusCodes.Status200OK)]
    public async Task<ActionResult<ApiResponse<PagedResult<TaskDto>>>> GetTasks([FromQuery] TaskQueryRequest request)
    {
        request.UserId = GetCurrentUserId();
        var tasks = await _taskService.GetTasksAsync(request);
        
        return Ok(ApiResponse<PagedResult<TaskDto>>.Success(tasks));
    }
    
    /// <summary>
    /// 获取任务详情
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <returns>任务详情</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(ApiResponse<TaskDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse), StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ApiResponse<TaskDto>>> GetTask(Guid id)
    {
        var task = await _taskService.GetTaskAsync(id);
        
        if (task == null)
        {
            return NotFound(ApiResponse.Error("TASK_NOT_FOUND", "任务不存在"));
        }
        
        return Ok(ApiResponse<TaskDto>.Success(task));
    }
    
    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        return Guid.Parse(userIdClaim!);
    }
}
```

---

## 📝 变更记录

| 版本 | 日期 | 变更内容 | 变更人 | 审批人 |
|------|------|----------|--------|--------|
| v2.0 | 2025-07-29 | 企业级开发实施指南重构，提供详细的开发步骤和最佳实践 | 技术团队 | 技术负责人 |
| v1.0 | 2025-07-26 | 初始开发指南文档创建 | 开发团队 | 架构师 |

### 🔄 下次更新计划
- **计划日期**: 2025-08-12
- **更新内容**: 根据开发实践优化指南内容
- **负责人**: 技术负责人

---

**文档维护**: 本文档由技术团队维护，根据开发进展持续更新
**实施反馈**: 开发过程中的问题和改进建议请及时反馈
**版权声明**: 本文档为ToDoListArea项目内部文档，未经授权不得外传
